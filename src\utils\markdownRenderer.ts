/**
 * Enhanced markdown renderer using marked with basic features
 */

import { marked } from 'marked';
import DOMPurify from 'dompurify';

// Configure marked with enhanced options
marked.setOptions({
  gfm: true,
  breaks: true
});

/**
 * Render markdown to HTML with enhanced features
 */
export function renderMarkdownToHtml(markdown: string): string {
  try {
    let html = marked(markdown);

    // Post-process HTML to add enhanced features
    html = addCopyButtonsToCodeBlocks(html);
    html = addAnchorLinksToHeadings(html);
    html = wrapTablesWithScrollable(html);

    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'br', 'strong', 'em', 'u', 'del', 's', 'ins', 'mark', 'sub', 'sup',
        'a', 'img', 'video', 'audio', 'source',
        'ul', 'ol', 'li', 'dl', 'dt', 'dd',
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        'blockquote', 'pre', 'code',
        'div', 'span', 'section', 'article', 'aside', 'header', 'footer', 'nav',
        'button', 'details', 'summary',
        'hr'
      ],
      ALLOWED_ATTR: [
        'id', 'class', 'href', 'title', 'alt', 'src', 'width', 'height',
        'data-code', 'data-lang', 'target', 'rel', 'type', 'controls', 'autoplay', 'loop', 'muted',
        'colspan', 'rowspan', 'align', 'valign',
        'style', 'lang', 'dir'
      ],
      ALLOW_DATA_ATTR: true
    });
  } catch (error) {
    console.error('Markdown rendering error:', error);
    return `<p>Error rendering markdown: ${String(error)}</p>`;
  }
}

/**
 * Add copy buttons to code blocks
 */
function addCopyButtonsToCodeBlocks(html: string): string {
  return html.replace(/<pre><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/g, (match, lang, code) => {
    const decodedCode = code.replace(/&lt;/g, '<').replace(/&gt;/g, '>').replace(/&amp;/g, '&');
    return `
      <div class="code-block-wrapper">
        <div class="code-block-header">
          <span class="code-language">${lang || 'text'}</span>
          <button class="copy-code-btn" data-code="${encodeURIComponent(decodedCode)}">Copy</button>
        </div>
        <pre><code class="language-${lang || 'text'}">${code}</code></pre>
      </div>
    `;
  });
}

/**
 * Add anchor links to headings
 */
function addAnchorLinksToHeadings(html: string): string {
  return html.replace(/<h([1-6])([^>]*)>(.*?)<\/h[1-6]>/g, (match, level, attrs, content) => {
    const id = content.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
    return `<h${level}${attrs} id="${id}">${content}<a href="#${id}" class="anchor-link">#</a></h${level}>`;
  });
}

/**
 * Wrap tables with scrollable container
 */
function wrapTablesWithScrollable(html: string): string {
  return html.replace(/<table>/g, '<div class="table-wrapper"><table class="markdown-table">').replace(/<\/table>/g, '</table></div>');
}

/**
 * Extract headings from markdown for table of contents
 */
export function extractHeadingsFromMarkdown(markdown: string): Array<{ level: number; text: string; id: string }> {
  const headings: Array<{ level: number; text: string; id: string }> = [];
  const lines = markdown.split('\n');

  for (const line of lines) {
    const match = line.match(/^(#{1,6})\s+(.+)$/);
    if (match) {
      const level = match[1].length;
      const text = match[2].trim();
      const id = text.toLowerCase().replace(/[^\w\s-]/g, '').replace(/\s+/g, '-');
      headings.push({ level, text, id });
    }
  }

  return headings;
}

export default marked;
