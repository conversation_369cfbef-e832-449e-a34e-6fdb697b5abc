import type { Metada<PERSON> } from "next";
import "./globals.css";
import { AppProvider } from "@/contexts/AppContext";

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export const metadata: Metadata = {
  title: "Markdown Editor - Professional Markdown Editing",
  description: "A powerful, feature-rich markdown editor with real-time preview, file management, and export capabilities.",
  keywords: ["markdown", "editor", "preview", "export", "writing"],
  authors: [{ name: "Markdown Editor Team" }],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased">
        <AppProvider>
          {children}
        </AppProvider>
      </body>
    </html>
  );
}
